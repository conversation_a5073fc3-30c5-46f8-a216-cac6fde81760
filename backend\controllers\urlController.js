import Url from '../models/Url.js';
import { generateShortUrl } from '../utils/generateShortUrl.js';

export const shortenUrl = async (req, res) => {
  try {
    //todo - implement shortenUrl

  } catch (error) {
  }
};

export const getAllUrls = async (req, res) => {
  try {
    //todo - implement getAllUrls
  } catch (error) {
  }
};

export const redirectUrl = async (req, res) => {
  try {
    //todo - implement redirectUrl
  } catch (error) {
  }
}; 