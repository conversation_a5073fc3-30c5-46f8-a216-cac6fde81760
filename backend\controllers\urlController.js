import Url from '../models/Url.js';
import MemoryUrl from '../models/MemoryUrl.js';
import { generateShortUrl } from '../utils/generateShortUrl.js';

// Use memory database if MongoDB is not available
const useMemoryDB = process.env.USE_MEMORY_DB === 'true';
const UrlModel = useMemoryDB ? MemoryUrl : Url;

export const shortenUrl = async (req, res) => {
  try {
    const { originalUrl } = req.body;

    if (!originalUrl) {
      return res.status(400).json({ error: 'Original URL is required' });
    }

    // Validate URL format
    const urlRegex = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;
    if (!urlRegex.test(originalUrl)) {
      return res.status(400).json({ error: 'Invalid URL format' });
    }

    // Check if URL already exists
    let existingUrl;
    if (useMemoryDB) {
      existingUrl = await UrlModel.findOne({ originalUrl });
    } else {
      existingUrl = await UrlModel.findOne({ originalUrl });
    }

    if (existingUrl) {
      return res.json({
        originalUrl: existingUrl.originalUrl,
        shortUrl: existingUrl.shortUrl,
        urlCode: existingUrl.urlCode,
        clicks: existingUrl.clicks,
        createdAt: existingUrl.createdAt
      });
    }

    // Generate short URL
    const urlCode = generateShortUrl();
    const shortUrl = `${req.protocol}://${req.get('host')}/${urlCode}`;

    // Create new URL entry
    let newUrl;
    if (useMemoryDB) {
      newUrl = await UrlModel.create({
        originalUrl,
        shortUrl,
        urlCode
      });
    } else {
      newUrl = new UrlModel({
        originalUrl,
        shortUrl,
        urlCode
      });
      await newUrl.save();
    }

    res.status(201).json({
      originalUrl: newUrl.originalUrl,
      shortUrl: newUrl.shortUrl,
      urlCode: newUrl.urlCode,
      clicks: newUrl.clicks,
      createdAt: newUrl.createdAt
    });

  } catch (error) {
    console.error('Error in shortenUrl:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

export const getAllUrls = async (req, res) => {
  try {
    let urls;
    if (useMemoryDB) {
      urls = await UrlModel.find();
    } else {
      urls = await UrlModel.find().sort({ createdAt: -1 });
    }
    res.json(urls);
  } catch (error) {
    console.error('Error in getAllUrls:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

export const redirectUrl = async (req, res) => {
  try {
    const { shortUrl } = req.params;

    let url;
    if (useMemoryDB) {
      url = await UrlModel.findOne({ urlCode: shortUrl });
    } else {
      url = await UrlModel.findOne({ urlCode: shortUrl });
    }

    if (!url) {
      return res.status(404).json({ error: 'URL not found' });
    }

    // Increment click count
    url.clicks += 1;
    if (useMemoryDB) {
      await UrlModel.save(url);
    } else {
      await url.save();
    }

    // Redirect to original URL
    res.redirect(url.originalUrl);

  } catch (error) {
    console.error('Error in redirectUrl:', error);
    res.status(500).json({ error: 'Server error' });
  }
};