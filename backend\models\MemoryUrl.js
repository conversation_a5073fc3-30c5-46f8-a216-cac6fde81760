// Simple in-memory storage for demonstration
class MemoryUrl {
  constructor() {
    this.urls = [];
    this.nextId = 1;
  }

  async create(data) {
    const newUrl = {
      _id: this.nextId++,
      originalUrl: data.originalUrl,
      shortUrl: data.shortUrl,
      urlCode: data.urlCode,
      clicks: data.clicks || 0,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    this.urls.push(newUrl);
    return newUrl;
  }

  async findOne(query) {
    return this.urls.find(url => {
      if (query.originalUrl) return url.originalUrl === query.originalUrl;
      if (query.urlCode) return url.urlCode === query.urlCode;
      return false;
    });
  }

  async find() {
    return [...this.urls].sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
  }

  async save(url) {
    const index = this.urls.findIndex(u => u._id === url._id);
    if (index !== -1) {
      url.updatedAt = new Date();
      this.urls[index] = url;
    }
    return url;
  }
}

// Create a singleton instance
const memoryUrlInstance = new MemoryUrl();

export default memoryUrlInstance;
