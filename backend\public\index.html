<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL Shortener</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="url"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            display: none;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .urls-list {
            margin-top: 30px;
        }
        .url-item {
            background: #f8f9fa;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .short-url {
            color: #007bff;
            text-decoration: none;
            font-weight: bold;
        }
        .short-url:hover {
            text-decoration: underline;
        }
        .clicks {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 URL Shortener</h1>
        
        <form id="shortenForm">
            <div class="form-group">
                <label for="originalUrl">Enter URL to shorten:</label>
                <input type="url" id="originalUrl" placeholder="https://example.com" required>
            </div>
            <button type="submit">Shorten URL</button>
        </form>

        <div id="result" class="result"></div>

        <div class="urls-list">
            <h2>Recent URLs</h2>
            <div id="urlsList"></div>
        </div>
    </div>

    <script>
        const API_BASE = '/api';

        document.getElementById('shortenForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const originalUrl = document.getElementById('originalUrl').value;
            const resultDiv = document.getElementById('result');
            
            try {
                const response = await fetch(`${API_BASE}/shorten`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ originalUrl })
                });

                const data = await response.json();

                if (response.ok) {
                    resultDiv.className = 'result';
                    resultDiv.innerHTML = `
                        <h3>✅ URL Shortened Successfully!</h3>
                        <p><strong>Original URL:</strong> ${data.originalUrl}</p>
                        <p><strong>Short URL:</strong> <a href="${data.shortUrl}" target="_blank">${data.shortUrl}</a></p>
                        <p><strong>Clicks:</strong> ${data.clicks}</p>
                    `;
                    resultDiv.style.display = 'block';
                    
                    // Clear the form
                    document.getElementById('originalUrl').value = '';
                    
                    // Refresh the URLs list
                    loadUrls();
                } else {
                    throw new Error(data.error || 'Failed to shorten URL');
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<h3>❌ Error</h3><p>${error.message}</p>`;
                resultDiv.style.display = 'block';
            }
        });

        async function loadUrls() {
            try {
                const response = await fetch(`${API_BASE}/urls`);
                const urls = await response.json();
                
                const urlsList = document.getElementById('urlsList');
                
                if (urls.length === 0) {
                    urlsList.innerHTML = '<p>No URLs shortened yet.</p>';
                    return;
                }
                
                urlsList.innerHTML = urls.map(url => `
                    <div class="url-item">
                        <div><strong>Original:</strong> ${url.originalUrl}</div>
                        <div><strong>Short:</strong> <a href="${url.shortUrl}" class="short-url" target="_blank">${url.shortUrl}</a></div>
                        <div class="clicks">Clicks: ${url.clicks} | Created: ${new Date(url.createdAt).toLocaleString()}</div>
                    </div>
                `).join('');
            } catch (error) {
                console.error('Failed to load URLs:', error);
                document.getElementById('urlsList').innerHTML = '<p>Failed to load URLs.</p>';
            }
        }

        // Load URLs when page loads
        loadUrls();
    </script>
</body>
</html>
