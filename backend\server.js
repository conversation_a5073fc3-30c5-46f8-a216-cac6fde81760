import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import connectDB from './config/database.js';
import urlRoutes from './routes/urlRoutes.js';
import { errorHandler, notFound } from './middlewares/errorHandler.js';

console.log('Starting server...');
dotenv.config();
console.log('Environment loaded');

const app = express();
const PORT = process.env.PORT || 5000;

console.log('Express app created');
console.log('USE_MEMORY_DB:', process.env.USE_MEMORY_DB);

// Only connect to MongoDB if not using memory database
if (process.env.USE_MEMORY_DB !== 'true') {
  console.log('Attempting to connect to MongoDB...');
  connectDB();
} else {
  console.log('Using in-memory database for demonstration');
}


// app.use(cors({
//   origin: process.env.NODE_ENV === 'production' 
//     ? ['https://yourfrontend.com'] 
//     : ['http://localhost:3000', 'http://localhost:5173'], // Common dev ports
//   credentials: true, // If you need to send cookies
//   optionsSuccessStatus: 200
// }));
console.log('Setting up middleware...');
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

console.log('Setting up routes...');
app.use('/api', urlRoutes);

// Serve the main page
app.get('/', (req, res) => {
  res.sendFile('index.html', { root: 'public' });
});

// Handle short URL redirects (this should come after the main route)
app.use('/', urlRoutes);

console.log('Setting up error handlers...');
app.use(notFound);
app.use(errorHandler);

console.log('Starting server on port', PORT);
app.listen(PORT, () => {
  console.log(`✅ Server running on port ${PORT}`);
  console.log(`🌐 Server URL: http://localhost:${PORT}`);
  console.log(`📋 API endpoints:`);
  console.log(`   POST http://localhost:${PORT}/api/shorten - Shorten URL`);
  console.log(`   GET  http://localhost:${PORT}/api/urls - Get all URLs`);
  console.log(`   GET  http://localhost:${PORT}/:shortUrl - Redirect to original URL`);
});